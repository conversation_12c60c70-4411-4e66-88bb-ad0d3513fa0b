"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, <PERSON>t, Zap, Target, Brain, Code } from 'lucide-react';

export default function AP3XSummary() {
  return (
    <div className="space-y-4">
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            AP3X Autonomous Development
          </CardTitle>
          <CardDescription className="text-[#666]">
            Advanced AI-powered development ecosystem
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Bot className="w-4 h-4 text-purple-400" />
                <span className="text-white">AI Agents</span>
              </div>
              <p className="text-[#666]">Multiple specialized agents working together</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-white">Real-time</span>
              </div>
              <p className="text-[#666]">Live updates and streaming responses</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Target className="w-4 h-4 text-green-400" />
                <span className="text-white">Goal-oriented</span>
              </div>
              <p className="text-[#666]">Task planning and execution</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Brain className="w-4 h-4 text-blue-400" />
                <span className="text-white">Context-aware</span>
              </div>
              <p className="text-[#666]">Deep understanding of your codebase</p>
            </div>
          </div>

          <div className="pt-4 border-t border-[#1a1a1a]">
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="bg-purple-900/20 text-purple-400 border-purple-800">
                <Code className="w-3 h-3 mr-1" />
                Code Generation
              </Badge>
              <Badge variant="secondary" className="bg-purple-900/20 text-purple-400 border-purple-800">
                <Bot className="w-3 h-3 mr-1" />
                AI Planning
              </Badge>
              <Badge variant="secondary" className="bg-purple-900/20 text-purple-400 border-purple-800">
                <Zap className="w-3 h-3 mr-1" />
                Real-time
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
