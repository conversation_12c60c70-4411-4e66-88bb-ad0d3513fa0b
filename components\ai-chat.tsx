"use client"

import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { useDyadChats, useDyadModels, useDyadStreaming } from '@/hooks/use-dyad';
import { useSocket } from '@/hooks/use-socket';
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  User,
  Send,
  Paperclip,
  Mic,
  Square,
  Copy,
  ThumbsUp,
  ThumbsDown,
  Sparkles,
  Plus,
  Settings,
  MessageSquare,
  CheckCircle,
  Clock,
  AlertCircle,
  Code,
  FileText,
  Zap,
  Brain,
  Target,
  Users,
  Loader2
} from "lucide-react"
import { App, Chat, ChatMessage } from '@/lib/dyad-client';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Enhanced message interface with all features
interface Message {
  id: string
  role: "user" | "assistant" | "agent"
  content: string
  timestamp: Date
  isStreaming?: boolean
  agentName?: string
  metadata?: {
    model?: string
    tokens?: number
    confidence?: number
    personality?: string
    capabilities?: string[]
    taskId?: string
    progress?: number
  }
}

// Chat task interface for task management (different from TaskList Task)
interface ChatTask {
  id: string;
  title: string;
  description: string;
  type: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  estimatedHours: number;
  agentUpdates: string[];
}

// Agent personalities
const agentPersonalities = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm excited to work together on this project. What should we build?",
    emoji: '🤝'
  },
  technical: {
    name: 'Technical Expert',
    tone: 'precise and analytical',
    greeting: "Hello! I'm here to provide technical expertise and detailed solutions.",
    emoji: '⚡'
  },
  creative: {
    name: 'Creative Innovator',
    tone: 'imaginative and inspiring',
    greeting: "Hi! Let's create something amazing together. I'm full of ideas!",
    emoji: '🎨'
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way.",
    emoji: '📚'
  }
};

interface AIChatProps {
  selectedApp?: App;
  messages?: Message[]
  onSendMessage?: (message: string) => void
  isLoading?: boolean
  model?: string
}

export default function AIChat({ selectedApp, messages: propMessages, onSendMessage: propOnSendMessage, isLoading: propIsLoading = false, model: propModel }: AIChatProps) {
  // Dyad hooks for real backend integration
  const { chats, createChat, deleteChat, addMessage } = useDyadChats(selectedApp?.id);
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();
  const socket = useSocket();

  // State management
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [chatMessages, setChatMessages] = useState<Message[]>(propMessages || []);
  const [input, setInput] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [selectedModel, setSelectedModel] = useState('moonshotai/kimi-k2');
  const [agentPersonality, setAgentPersonality] = useState('collaborative');
  const [tasks, setTasks] = useState<ChatTask[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null)

  // OpenRouter models (user's preferred models)
  const openRouterModels = [
    'moonshotai/kimi-k2',
    'x-ai/grok-4',
    'anthropic/claude-sonnet-4',
    'anthropic/claude-3.5-sonnet',
    'openai/gpt-4o',
    'openai/gpt-4-turbo',
    'google/gemini-pro-1.5',
    'meta-llama/llama-3.1-405b-instruct'
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [chatMessages])

  // Load chat messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadChatMessages();
    }
  }, [selectedChat]);

  // Socket integration for real-time updates
  useEffect(() => {
    if (socket && selectedApp) {
      socket.on('taskUpdate', (taskData: ChatTask) => {
        setTasks(prev => {
          const index = prev.findIndex(t => t.id === taskData.id);
          if (index >= 0) {
            const updated = [...prev];
            updated[index] = taskData;
            return updated;
          }
          return [...prev, taskData];
        });
      });

      socket.on('agentMessage', (messageData: Message) => {
        setChatMessages(prev => [...prev, messageData]);
      });

      return () => {
        socket.off('taskUpdate');
        socket.off('agentMessage');
      };
    }
  }, [socket, selectedApp]);

  const loadChatMessages = async () => {
    if (!selectedChat) return;

    try {
      setIsLoadingMessages(true);
      // Load messages from backend - this would be implemented in the dyad client
      // For now, we'll use the existing messages
    } catch (err) {
      console.error('Failed to load messages:', err);
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleCreateChat = async () => {
    if (!selectedApp) return;

    try {
      const newChat = await createChat(selectedApp.id, `Chat ${chats.length + 1}`);
      setSelectedChat(newChat);

      // Initialize with personality-based greeting
      const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        role: 'agent',
        content: `${personality.greeting} ${personality.emoji}`,
        timestamp: new Date(),
        agentName: personality.name,
        metadata: {
          personality: agentPersonality,
          model: selectedModel
        }
      };

      setChatMessages([welcomeMessage]);
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleSend = async () => {
    if (!input.trim() || (!selectedApp && !propOnSendMessage)) return;

    // Use prop function if provided (for backward compatibility)
    if (propOnSendMessage) {
      propOnSendMessage(input.trim());
      setInput("");
      return;
    }

    if (!selectedChat) {
      await handleCreateChat();
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);

    try {
      // Add user message to backend
      await addMessage(selectedChat.id, input.trim(), 'user');

      // Start streaming AI response
      await startStream(
        selectedChat.id,
        input.trim(),
        { name: selectedModel, provider: 'openrouter' },
        selectedApp!.id
      );

      // Handle streaming response
      if (streamContent) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: streamContent,
          timestamp: new Date(),
          metadata: {
            model: selectedModel,
            tokens: streamContent.length
          }
        };
        setChatMessages(prev => [...prev, aiMessage]);
      }
    } catch (err) {
      console.error('Failed to send message:', err);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'in-progress': return <Clock className="w-4 h-4 text-blue-400" />;
      case 'blocked': return <AlertCircle className="w-4 h-4 text-red-400" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const currentMessages = propMessages || chatMessages;
  const currentIsLoading = propIsLoading || isStreaming || isTyping;

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Enhanced Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-green-500 rounded-full animate-ping opacity-75"></div>
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp ? `${selectedApp.name} - AI Assistant` : 'AI Chat'}
            </h2>
            <p className="text-xs text-[#666]">
              {agentPersonalities[agentPersonality as keyof typeof agentPersonalities].tone}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Model Selector */}
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48 h-8 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {openRouterModels.map((model) => (
                <SelectItem key={model} value={model} className="text-xs">
                  {model}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Agent Personality Selector */}
          <Select value={agentPersonality} onValueChange={setAgentPersonality}>
            <SelectTrigger className="w-40 h-8 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {Object.entries(agentPersonalities).map(([key, personality]) => (
                <SelectItem key={key} value={key} className="text-xs">
                  {personality.emoji} {personality.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* New Chat Button */}
          {selectedApp && (
            <Button
              onClick={handleCreateChat}
              size="sm"
              className="h-8 bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-1" />
              New Chat
            </Button>
          )}
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-4">
              <AnimatePresence>
                {currentMessages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className={`flex gap-3 ${message.role === "user" ? "flex-row-reverse" : ""}`}
                  >
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          message.role === "user"
                            ? "bg-blue-500"
                            : message.role === "agent"
                            ? "bg-purple-500"
                            : "bg-gray-600"
                        }`}
                      >
                        {message.role === "user" ? (
                          <User className="w-4 h-4 text-white" />
                        ) : (
                          <Bot className="w-4 h-4 text-white" />
                        )}
                      </div>
                    </div>

                    <div className={`flex-1 max-w-[80%] ${message.role === "user" ? "text-right" : ""}`}>
                      <Card
                        className={`${
                          message.role === "user"
                            ? "bg-blue-500 text-white border-blue-500"
                            : message.role === "agent"
                            ? "bg-purple-500/20 border-purple-500/30 text-gray-100"
                            : "bg-[#1a1a1a] border-[#333] text-gray-100"
                        }`}
                      >
                        <CardContent className="p-3">
                          {message.agentName && (
                            <div className="text-xs font-medium mb-1 opacity-80">
                              {message.agentName}
                            </div>
                          )}
                          <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>

                          {message.isStreaming && (
                            <div className="flex items-center gap-2 mt-2 text-xs opacity-70">
                              <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                              <span>AI is thinking...</span>
                            </div>
                          )}

                          {message.metadata && (message.role === "assistant" || message.role === "agent") && (
                            <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-600">
                              <div className="flex items-center gap-2 text-xs text-gray-400">
                                <Badge variant="outline" className="text-xs px-1 py-0">
                                  {message.metadata.model || propModel || 'AI'}
                                </Badge>
                                {message.metadata.tokens && <span>{message.metadata.tokens} tokens</span>}
                                {message.metadata.confidence && (
                                  <span className="flex items-center gap-1">
                                    <Sparkles className="w-3 h-3" />
                                    {Math.round(message.metadata.confidence * 100)}%
                                  </span>
                                )}
                                {message.metadata.personality && (
                                  <Badge variant="outline" className="text-xs px-1 py-0">
                                    {agentPersonalities[message.metadata.personality as keyof typeof agentPersonalities]?.emoji}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0"
                                  onClick={() => copyMessage(message.content)}
                                >
                                  <Copy className="w-3 h-3" />
                                </Button>
                                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                  <ThumbsUp className="w-3 h-3" />
                                </Button>
                                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                                  <ThumbsDown className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <div className={`text-xs text-gray-500 mt-1 ${message.role === "user" ? "text-right" : ""}`}>
                        {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {currentIsLoading && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex gap-3"
                >
                  <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <Card className="bg-[#1a1a1a] border-[#333]">
                    <CardContent className="p-3">
                      <div className="flex items-center gap-2 text-gray-400">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                          <div
                            className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.1s" }}
                          />
                          <div
                            className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.2s" }}
                          />
                        </div>
                        <span className="text-sm">
                          {isStreaming ? 'Streaming response...' : 'AI is processing...'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Enhanced Input Area */}
          <div className="border-t border-[#333] p-4">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {selectedModel || propModel || 'AI'}
              </Badge>
              <div className="w-2 h-2 bg-green-400 rounded-full" />
              <span className="text-xs text-gray-400">
                {isStreaming ? 'Streaming...' : 'Online'}
              </span>
              {selectedApp && (
                <>
                  <div className="w-1 h-1 bg-gray-600 rounded-full" />
                  <span className="text-xs text-gray-400">{selectedApp.name}</span>
                </>
              )}
            </div>

            <div className="relative">
              <Input
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  selectedApp
                    ? `Ask ${agentPersonalities[agentPersonality as keyof typeof agentPersonalities].name} to help with ${selectedApp.name}...`
                    : "Ask AI anything..."
                }
                className="bg-[#1e1e1e] border-[#333] pr-20 focus:border-blue-500 transition-colors"
                disabled={currentIsLoading}
              />
              <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={() => setIsRecording(!isRecording)}
                >
                  {isRecording ? (
                    <Square className="w-4 h-4 text-red-400" />
                  ) : (
                    <Mic className="w-4 h-4 text-gray-400" />
                  )}
                </Button>
                <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                  <Paperclip className="w-4 h-4 text-gray-400" />
                </Button>
                <Button
                  size="sm"
                  className="h-8 w-8 p-0 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600"
                  onClick={handleSend}
                  disabled={!input.trim() || currentIsLoading}
                >
                  {currentIsLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Task Panel */}
        {showTaskPanel && tasks.length > 0 && selectedApp && (
          <div className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]/30 flex flex-col">
            <div className="p-4 border-b border-[#1a1a1a]">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-white text-sm">Active Tasks</h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowTaskPanel(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <ScrollArea className="flex-1 p-4">
              <div className="space-y-3">
                {tasks.map((task) => (
                  <motion.div
                    key={task.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="bg-[#1a1a1a] rounded-lg p-3 border border-[#333]"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(task.status)}
                        <span className="text-sm font-medium text-white">{task.title}</span>
                      </div>
                      <Badge
                        variant="outline"
                        className={cn(
                          "text-xs",
                          task.status === 'completed' && "border-green-500 text-green-400",
                          task.status === 'in-progress' && "border-blue-500 text-blue-400",
                          task.status === 'blocked' && "border-red-500 text-red-400"
                        )}
                      >
                        {task.status}
                      </Badge>
                    </div>

                    <p className="text-xs text-gray-400 mb-2">{task.description}</p>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">Progress</span>
                        <span className="text-gray-400">{task.progress}%</span>
                      </div>
                      <Progress value={task.progress} className="h-1" />
                    </div>

                    <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                      <span>{task.type}</span>
                      <span>{task.estimatedHours}h est.</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    </div>
  )
}
