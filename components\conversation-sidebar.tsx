"use client"

import React, { useState, useEffect } from 'react';
import { useDyadChats, useDyadApps } from '@/hooks/use-dyad';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  MessageSquare, 
  Plus, 
  Search, 
  Trash2, 
  Calendar,
  User,
  Bot,
  ChevronLeft,
  Folder
} from 'lucide-react';
import { Chat, App } from '@/lib/dyad-client';

interface ConversationSidebarProps {
  isOpen: boolean;
  onSelectChat: (chat: Chat) => void;
  selectedChatId?: number;
  selectedApp?: App;
  onCreateChat: () => void;
}

export default function ConversationSidebar({ 
  isOpen, 
  onSelectChat, 
  selectedChatId, 
  selectedApp,
  onCreateChat 
}: ConversationSidebarProps) {
  const { chats, createChat, deleteChat } = useDyadChats(selectedApp?.id);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredChats, setFilteredChats] = useState<Chat[]>([]);

  // Filter chats based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredChats(chats);
    } else {
      const filtered = chats.filter(chat => 
        chat.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        chat.messages?.some(msg => 
          msg.content.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
      setFilteredChats(filtered);
    }
  }, [chats, searchQuery]);

  const handleCreateChat = async () => {
    if (!selectedApp) return;
    
    try {
      const newChat = await createChat(selectedApp.id, `Chat ${chats.length + 1}`);
      onSelectChat(newChat);
      onCreateChat();
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleDeleteChat = async (chat: Chat, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteChat(chat.id);
    } catch (err) {
      console.error('Failed to delete chat:', err);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getLastMessage = (chat: Chat) => {
    if (!chat.messages || chat.messages.length === 0) return 'No messages';
    const lastMessage = chat.messages[chat.messages.length - 1];
    return lastMessage.content.length > 50 
      ? lastMessage.content.substring(0, 50) + '...'
      : lastMessage.content;
  };

  const getMessageCount = (chat: Chat) => {
    return chat.messages?.length || 0;
  };

  return (
    <>
      {/* Hover trigger area */}
      <div 
        className={`fixed left-0 top-0 w-2 h-full z-40 ${!isOpen ? 'hover:bg-blue-500/20' : ''}`}
        style={{ transition: 'background-color 0.2s ease' }}
      />
      
      {/* Slide-out panel */}
      <div
        className={`fixed left-0 top-0 h-full bg-[#0a0a0a] border-r border-[#1a1a1a] shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ width: '320px' }}
      >
        {/* Header */}
        <div className="h-14 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium text-white">Conversations</span>
          </div>
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCreateChat}
              disabled={!selectedApp}
              className="h-7 w-7 p-0 text-[#888] hover:text-white hover:bg-[#1a1a1a]"
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* App Context */}
        {selectedApp && (
          <div className="px-4 py-3 bg-[#0a0a0a] border-b border-[#1a1a1a]">
            <div className="flex items-center gap-2">
              <Folder className="w-3 h-3 text-blue-500" />
              <span className="text-xs text-[#888]">App:</span>
              <span className="text-xs text-white font-medium">{selectedApp.name}</span>
            </div>
          </div>
        )}

        {/* Search */}
        <div className="p-3 border-b border-[#1a1a1a]">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-3 h-3 text-[#666]" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search conversations..."
              className="pl-9 h-8 bg-[#111111] border-[#333] text-white text-xs placeholder:text-[#666]"
            />
          </div>
        </div>

        {/* Chat List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {!selectedApp ? (
              <div className="text-center py-8 text-[#666]">
                <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-xs">Select an app to view conversations</p>
              </div>
            ) : filteredChats.length === 0 ? (
              <div className="text-center py-8 text-[#666]">
                <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-xs">
                  {searchQuery ? 'No conversations found' : 'No conversations yet'}
                </p>
                {!searchQuery && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCreateChat}
                    className="mt-3 h-7 px-3 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white hover:bg-[#2a2a2a] text-xs"
                  >
                    Start First Chat
                  </Button>
                )}
              </div>
            ) : (
              <div className="space-y-1">
                {filteredChats.map((chat) => (
                  <div
                    key={chat.id}
                    onClick={() => onSelectChat(chat)}
                    className={`group p-3 rounded-lg cursor-pointer transition-colors hover:bg-[#1a1a1a] ${
                      selectedChatId === chat.id ? 'bg-[#1a1a1a] border border-blue-500/30' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-xs font-medium text-white truncate">
                            {chat.title || `Chat ${chat.id}`}
                          </h4>
                          <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-1 py-0">
                            {getMessageCount(chat)}
                          </Badge>
                        </div>
                        <p className="text-[10px] text-[#666] line-clamp-2 leading-relaxed">
                          {getLastMessage(chat)}
                        </p>
                        <div className="flex items-center gap-1 mt-2">
                          <Calendar className="w-2.5 h-2.5 text-[#555]" />
                          <span className="text-[10px] text-[#555]">
                            {formatDate(chat.updatedAt)}
                          </span>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => handleDeleteChat(chat, e)}
                        className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="h-12 bg-[#111111] border-t border-[#1a1a1a] flex items-center justify-between px-4">
          <div className="text-[10px] text-[#666]">
            {filteredChats.length} conversation{filteredChats.length !== 1 ? 's' : ''}
          </div>
          <div className="text-[10px] text-[#666]">
            {selectedApp ? selectedApp.name : 'No app selected'}
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/20 z-40"
          onClick={() => {}} // Prevent closing on overlay click for now
        />
      )}
    </>
  );
}
