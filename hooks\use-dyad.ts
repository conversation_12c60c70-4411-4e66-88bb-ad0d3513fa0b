import { useState, useEffect, useCallback } from 'react';
import { apiClient, App, Cha<PERSON>, ChatMessage, LanguageModel } from '@/lib/dyad-client';
import { useSocket } from './use-socket';

export function useDyad() {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Test connection on mount
  useEffect(() => {
    const testConnection = async () => {
      try {
        await apiClient.healthCheck();
        setIsConnected(true);
        setError(null);
      } catch (err) {
        setIsConnected(false);
        setError(err instanceof Error ? err.message : 'Connection failed');
      } finally {
        setIsLoading(false);
      }
    };

    testConnection();
  }, []);

  return {
    isConnected,
    isLoading,
    error,
    client: apiClient,
  };
}

export function useDyadApps() {
  const [apps, setApps] = useState<App[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [appStatuses, setAppStatuses] = useState<Record<number, { isRunning: boolean; url: string | null; port?: number }>>({});

  // Socket.IO integration for real-time app status updates
  useSocket({
    onAppStatusChanged: (update) => {
      setAppStatuses(prev => ({
        ...prev,
        [update.appId]: {
          isRunning: update.isRunning,
          url: update.url,
          port: update.port,
        }
      }));
    }
  });

  const loadApps = useCallback(async () => {
    try {
      setIsLoading(true);
      const appList = await apiClient.listApps();
      setApps(appList);
      setError(null);

      // Load status for each app
      const statusPromises = appList.map(async (app) => {
        try {
          const status = await apiClient.getAppStatus(app.id);
          return { appId: app.id, status };
        } catch {
          return { appId: app.id, status: { isRunning: false, url: null } };
        }
      });

      const statuses = await Promise.all(statusPromises);
      const statusMap = statuses.reduce((acc, { appId, status }) => {
        acc[appId] = status;
        return acc;
      }, {} as Record<number, { isRunning: boolean; url: string | null; port?: number }>);

      setAppStatuses(statusMap);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load apps');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createApp = useCallback(async (name: string, template?: string) => {
    try {
      const newApp = await apiClient.createApp({ name, template });
      setApps(prev => [newApp, ...prev]);
      return newApp;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create app');
      throw err;
    }
  }, []);

  const deleteApp = useCallback(async (id: number) => {
    try {
      await apiClient.deleteApp(id);
      setApps(prev => prev.filter(app => app.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete app');
      throw err;
    }
  }, []);

  const startApp = useCallback(async (id: number) => {
    try {
      await apiClient.startApp(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start app');
      throw err;
    }
  }, []);

  const stopApp = useCallback(async (id: number) => {
    try {
      await apiClient.stopApp(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to stop app');
      throw err;
    }
  }, []);

  useEffect(() => {
    loadApps();
  }, []); // Remove loadApps dependency to prevent infinite loop

  return {
    apps,
    isLoading,
    error,
    appStatuses,
    createApp,
    deleteApp,
    startApp,
    stopApp,
    refreshApps: loadApps,
    getAppStatus: (appId: number) => appStatuses[appId] || { isRunning: false, url: null },
  };
}

export function useDyadChats(appId?: number) {
  const [chats, setChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadChats = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log(`Loading chats for appId: ${appId || 'ALL'}`);
      const chatList = await apiClient.listChats(appId);
      console.log(`Loaded ${chatList.length} chats:`, chatList);

      // Filter out invalid chats (those with invalid IDs)
      const validChats = chatList.filter(chat =>
        chat &&
        chat.id &&
        Number.isInteger(chat.id) &&
        chat.id > 0
      );

      if (validChats.length !== chatList.length) {
        console.warn(`Filtered out ${chatList.length - validChats.length} invalid chats`);
      }

      setChats(validChats);
      setError(null);
    } catch (err) {
      console.error('Failed to load chats:', err);
      setError(err instanceof Error ? err.message : 'Failed to load chats');
    } finally {
      setIsLoading(false);
    }
  }, [appId]);

  const createChat = useCallback(async (appId: number, title?: string) => {
    try {
      const newChat = await apiClient.createChat({ appId, title });
      setChats(prev => [newChat, ...prev]);
      return newChat;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create chat');
      throw err;
    }
  }, []);

  const deleteChat = useCallback(async (id: number) => {
    try {
      await apiClient.deleteChat(id);
      setChats(prev => prev.filter(chat => chat.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete chat');
      throw err;
    }
  }, []);

  const addMessage = useCallback(async (chatId: number, content: string, role: 'user' | 'assistant' = 'user') => {
    try {
      const message = await apiClient.addMessage(chatId, { content, role });
      return message;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add message');
      throw err;
    }
  }, []);

  useEffect(() => {
    loadChats();
  }, [appId]); // Only depend on appId, not loadChats function

  return {
    chats,
    isLoading,
    error,
    createChat,
    deleteChat,
    addMessage,
    refreshChats: loadChats,
  };
}

export function useDyadModels() {
  const [models, setModels] = useState<LanguageModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadModels = useCallback(async () => {
    try {
      const modelList = await apiClient.getAllLanguageModels();
      setModels(modelList);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load models');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadModels();
  }, []); // Remove loadModels dependency to prevent infinite loop

  return {
    models,
    isLoading,
    error,
    refreshModels: () => {
      setIsLoading(true);
      loadModels();
    },
  };
}

export function useDyadProviders() {
  const [providers, setProviders] = useState<any[]>([]);
  const [modelsByProvider, setModelsByProvider] = useState<Record<string, any[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadProviders = useCallback(async () => {
    try {
      setIsLoading(true);
      const [providersData, modelsByProviderData] = await Promise.all([
        apiClient.getLanguageModelProviders(),
        apiClient.getLanguageModelsByProviders()
      ]);

      setProviders(providersData);
      setModelsByProvider(modelsByProviderData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load providers');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadProviders();
  }, []); // Remove loadProviders dependency to prevent infinite loop

  const getModelsForProvider = useCallback((providerId: string) => {
    return modelsByProvider[providerId] || [];
  }, [modelsByProvider]);

  return {
    providers,
    modelsByProvider,
    isLoading,
    error,
    refreshProviders: loadProviders,
    getModelsForProvider,
  };
}

export function useDyadStreaming() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamContent, setStreamContent] = useState('');
  const [error, setError] = useState<string | null>(null);

  const startStream = useCallback(async (
    chatId: number,
    message: string,
    model: { name: string; provider: string },
    appId: number
  ) => {
    try {
      setIsStreaming(true);
      setStreamContent('');
      setError(null);

      await apiClient.streamChat(
        chatId,
        { message, model, appId },
        (chunk) => {
          if (chunk.done) {
            setIsStreaming(false);
          } else {
            setStreamContent(prev => prev + chunk.content);
          }
        }
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Streaming failed');
      setIsStreaming(false);
    }
  }, []);

  const stopStream = useCallback(() => {
    setIsStreaming(false);
  }, []);

  const resetStream = useCallback(() => {
    setStreamContent('');
    setError(null);
  }, []);

  return {
    isStreaming,
    streamContent,
    error,
    startStream,
    stopStream,
    resetStream,
  };
}
