/**
 * AP3X Framework and Context Engine API Client
 * Provides integration with the autonomous development ecosystem
 */

import { apiClient } from './dyad-client';

export interface AP3XAgent {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'busy' | 'thinking' | 'working';
  capabilities: string[];
  personality: string;
  metadata: Record<string, any>;
}

export interface AP3XProjectPlan {
  id: string;
  title: string;
  description: string;
  phases: AP3XProjectPhase[];
  estimatedDuration: number;
  dependencies: string[];
  risks: string[];
  agentNotes: string[];
}

export interface AP3XProjectPhase {
  id: string;
  name: string;
  description: string;
  tasks: AP3XTask[];
  estimatedDuration: number;
  prerequisites: string[];
  agentFeedback: string;
}

export interface AP3XTask {
  id: string;
  title: string;
  description: string;
  type: 'feature' | 'bugfix' | 'refactor' | 'documentation' | 'optimization';
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedHours: number;
  dependencies: string[];
  assignedAgent?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  agentUpdates: string[];
  startedAt?: string;
  completedAt?: string;
}

export interface AP3XContextAnalysis {
  filesProcessed: number;
  nodesCreated: number;
  relationshipsCreated: number;
  codeComplexity: number;
  testCoverage: number;
  documentationCoverage: number;
  dependencies: string[];
  agentInsights: string[];
}

export interface AP3XCodeGenerationResult {
  code: string;
  language: string;
  framework: string;
  tests: string;
  documentation: string;
  dependencies: string[];
  estimatedComplexity: number;
  agentExplanation: string;
  nextSteps: string[];
}

export interface AP3XAgentStats {
  timestamp: string;
  ap3xFramework: {
    totalAgents: number;
    activeAgents: number;
    totalSessions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    currentTasks: AP3XTask[];
  };
  contextEngine: {
    database: {
      nodes: number;
      relationships: number;
      indexes: number;
    };
    ingestion: {
      totalFiles: number;
      processedFiles: number;
      failedFiles: number;
      averageProcessingTime: number;
    };
    retrieval: {
      totalQueries: number;
      successfulQueries: number;
      failedQueries: number;
      averageQueryTime: number;
    };
  };
}

export interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'system' | 'task-update' | 'progress' | 'insight';
  content: string;
  metadata?: {
    taskId?: string;
    progress?: number;
    status?: string;
    suggestions?: string[];
    codeSnippet?: string;
    filePath?: string;
  };
  timestamp: string;
  agentName?: string;
  agentStatus?: string;
}

export class AP3XClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const error = await response.json();
          errorMessage = error.error || error.message || errorMessage;
        } else {
          const textError = await response.text();
          errorMessage = textError || errorMessage;
        }
      } catch (e) {
        errorMessage = `HTTP ${response.status}`;
      }
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text() as Promise<T>;
    }
  }

  // Health check for AP3X ecosystem
  async getHealth(): Promise<{
    timestamp: string;
    ap3xFramework: any;
    contextEngine: any;
    status: string;
  }> {
    return this.request('/api/ap3x/health');
  }

  // List all available AP3X agents
  async getAgents(): Promise<AP3XAgent[]> {
    return this.request('/api/ap3x/agents');
  }

  // Create project plan using AP3X planning agent
  async createProjectPlan(appId: number, requirements: string, techStack?: string): Promise<{
    projectPlan: AP3XProjectPlan;
    context: AP3XContextAnalysis | null;
  }> {
    return this.request('/api/ap3x/project-plan', {
      method: 'POST',
      body: JSON.stringify({ appId, requirements, techStack }),
    });
  }

  // Create task breakdown using AP3X task planner
  async createTaskPlan(appId: number, featureRequest: string, priority?: string): Promise<{
    taskPlan: AP3XTask[];
    context: {
      relevantFiles: string[];
      totalContext: number;
    };
  }> {
    return this.request('/api/ap3x/task-plan', {
      method: 'POST',
      body: JSON.stringify({ appId, featureRequest, priority }),
    });
  }

  // Generate code using AP3X coding agents
  async generateCode(appId: number, task: {
    description: string;
    type: string;
    requirements: string[];
  }, agentType?: string): Promise<{
    codeResult: AP3XCodeGenerationResult;
    context: {
      relevantFiles: string[];
      totalContext: number;
    };
  }> {
    return this.request('/api/ap3x/code-generation', {
      method: 'POST',
      body: JSON.stringify({ appId, task, agentType }),
    });
  }

  // Get real-time task updates
  async getTaskUpdates(appId: number): Promise<{
    tasks: AP3XTask[];
    activeAgents: AP3XAgent[];
  }> {
    return this.request(`/api/ap3x/tasks/${appId}`);
  }

  // Update task status
  async updateTaskStatus(taskId: string, status: string, progress?: number): Promise<AP3XTask> {
    return this.request(`/api/ap3x/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify({ status, progress }),
    });
  }

  // Send agent message
  async sendAgentMessage(appId: number, message: string, agentType?: string): Promise<AgentMessage> {
    return this.request('/api/ap3x/agent-message', {
      method: 'POST',
      body: JSON.stringify({ appId, message, agentType }),
    });
  }

  // Get agent conversation history
  async getAgentConversation(appId: number): Promise<AgentMessage[]> {
    return this.request(`/api/ap3x/conversation/${appId}`);
  }

  // Stream agent updates
  async streamAgentUpdates(appId: number, onUpdate: (update: any) => void) {
    const response = await fetch(`${this.baseUrl}/api/ap3x/stream/${appId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onUpdate(data);
            } catch (e) {
              // Ignore malformed JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // Get framework statistics
  async getStats(): Promise<AP3XAgentStats> {
    return this.request('/api/ap3x/stats');
  }
}

// Export singleton instance
export const ap3xClient = new AP3XClient();
